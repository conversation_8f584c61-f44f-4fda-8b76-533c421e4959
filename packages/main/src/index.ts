import type { AppInitConfig } from './AppInitConfig.js'

import { app } from 'electron'
import { NestFactory } from '@nestjs/core'
import { existsSync, mkdirSync, writeFileSync } from 'fs'
import { join } from 'path'

import 'dotenv/config'
import 'reflect-metadata'

import { RootModule } from './root.module.js'
import { createModuleRunner } from '@/ModuleRunner.js'
import { createWindowManagerModule } from '@/app-modules/WindowManager.js'
import { disallowMultipleAppInstance } from '@/app-modules/SingleInstanceApp.js'
import { terminateAppOnLastWindowClose } from '@/app-modules/ApplicationTerminatorOnLastWindowClose.js'
import { hardwareAccelerationMode } from '@/app-modules/HardwareAccelerationModule.js'
import { autoUpdater } from './app-modules/AutoUpdater.js'
import { allowInternalOrigins } from '@/app-modules/BlockNotAllowdOrigins.js'
import { allowExternalUrls } from '@/app-modules/ExternalUrls.js'
import { Logger } from '@nestjs/common'

const logger = new Logger('main')

/**
 * 设置错误日志收集
 */
function setupErrorLogging() {
  const logDir = join(process.cwd(), 'logs')

  // 确保日志目录存在
  if (!existsSync(logDir)) {
    mkdirSync(logDir, { recursive: true })
  }

  const logFile = join(logDir, `error-${new Date().toISOString().split('T')[0]}.log`)

  function writeErrorLog(type: string, error: any) {
    const timestamp = new Date().toISOString()
    const errorInfo = {
      timestamp,
      type,
      message: error?.message || String(error),
      stack: error?.stack || '',
      pid: process.pid,
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      electronVersion: process.versions.electron
    }

    const logEntry = `${timestamp} [${type}] ${JSON.stringify(errorInfo, null, 2)}\n\n`

    try {
      writeFileSync(logFile, logEntry, { flag: 'a' })
      logger.error(`${type}:`, error)
    } catch (writeError) {
      logger.error('写入日志文件失败:', writeError)
      logger.error(`原始${type}:`, error)
    }
  }

  // 监听未捕获的异常
  process.on('uncaughtException', error => {
    writeErrorLog('UNCAUGHT_EXCEPTION', error)
    // 不要立即退出，让应用有机会清理
  })

  // 监听未处理的Promise拒绝
  process.on('unhandledRejection', (reason, promise) => {
    writeErrorLog('UNHANDLED_REJECTION', {
      reason,
      promise: promise.toString()
    })
  })

  logger.log(`错误日志将保存到: ${logFile}`)
}

export async function initApp(initConfig: AppInitConfig) {
  // 首先设置错误日志收集
  setupErrorLogging()
  await app.whenReady()

  // 创建 NestJS 应用上下文，这会触发所有模块的初始化
  const nestApp = await NestFactory.createApplicationContext(RootModule)
  await nestApp.init()

  // 添加应用关闭时的清理逻辑
  app.on('before-quit', async event => {
    logger.log('应用即将关闭，开始清理资源...')

    try {
      // 阻止默认关闭行为，等待清理完成
      event.preventDefault()

      // 关闭 NestJS 应用上下文，这会触发所有服务的清理
      await nestApp.close()

      logger.log('资源清理完成')

      // 现在可以安全退出
      app.exit(0)
    } catch (error) {
      logger.error('清理资源时出错:', error)
      // 即使清理失败也要退出
      app.exit(1)
    }
  })

  const moduleRunner = createModuleRunner()
    .init(createWindowManagerModule({ initConfig, openDevTools: true }))
    .init(disallowMultipleAppInstance())
    .init(terminateAppOnLastWindowClose())
    .init(hardwareAccelerationMode({ enable: true }))
    .init(autoUpdater())
    // Install DevTools extension if needed
    // .init(chromeDevToolsExtension({extension: 'VUEJS3_DEVTOOLS'}))

    // Security
    .init(allowInternalOrigins(
      new Set(initConfig.renderer instanceof URL ? [initConfig.renderer.origin] : []),
    ))
    .init(allowExternalUrls(
      new Set(
        initConfig.renderer instanceof URL
          ? [
            'https://vite.dev',
            'https://developer.mozilla.org',
            'https://solidjs.com',
            'https://qwik.dev',
            'https://lit.dev',
            'https://react.dev',
            'https://preactjs.com',
            'https://www.typescriptlang.org',
            'https://vuejs.org',
          ]
          : [],
      )),
    )

  await moduleRunner
}
